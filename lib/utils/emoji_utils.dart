import 'dart:convert';

import 'package:dada/generated/assets.dart';
import 'package:dada/model/custom_emoji_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:flutter/services.dart' show rootBundle;

class EmojiUtils extends GetxService {
  factory EmojiUtils() => Get.find<EmojiUtils>();

  EmojiUtils._() {
    loadEmojiFiles();
  }

  static init() {
    Get.put<EmojiUtils>(EmojiUtils._(), permanent: true);
  }

  List<CustomStickerPackage>? _customEmojiStickerPackageList;

  List<CustomStickerPackage>? get customEmojiStickerPackageList =>
      _customEmojiStickerPackageList;

  List<CustomEmojiFaceData>? _customEmojiFaceDataList;

  List<CustomEmojiFaceData>? get customEmojiFaceDataList =>
      _customEmojiFaceDataList;

  Future<void> loadEmojiFiles() async {
    final manifest = await rootBundle.loadString('AssetManifest.json');
    final List<String> filePaths = manifest.split('\n');
    final String filePathsJsonStr = filePaths.first;
    final Map<String, dynamic> filePathsMap = jsonDecode(filePathsJsonStr);
    final List<String> defaultEmojiPaths = filePathsMap.keys
        .where((filePath) => filePath
            .startsWith("assets/custom_face_resource/dada_default_emojis/"))
        .toList();
    List<CustomSticker> list = [];
    List<String> emojiList = [];
    int packageIconIndex = 0;
    for (int i = 0; i < defaultEmojiPaths.length; i++) {
      String emojiFilePath = defaultEmojiPaths[i];
      String emoji = emojiFilePath
          .split("assets/custom_face_resource/dada_default_emojis/")
          .last;
      CustomSticker sticker = CustomSticker(name: emoji, index: i, url: emoji);
      list.add(sticker);
      emojiList.add(emoji);
      if (emoji == "微笑.png") {
        packageIconIndex = i;
      }
    }

    if (list.isNotEmpty == true) {
      CustomSticker? sticker1 =
          list.where((e) => e.name == "微笑.png").toList().first;
      int index = list.indexOf(sticker1);
      list.removeAt(index);
      list.insert(0, sticker1);
    }

    if (emojiList.isNotEmpty == true) {
      String? emojiFirst = emojiList.where((e) => e == "微笑.png").toList().first;
      int index = emojiList.indexOf(emojiFirst);
      emojiList.removeAt(index);
      emojiList.insert(0, emojiFirst);
    }

    CustomStickerPackage customStickerPackage = CustomStickerPackage(
      isEmoji: true,
      name: "dada_default_emojis",
      stickerList: list,
      baseUrl: "assets/custom_face_resource/dada_default_emojis",
      menuItem: CustomSticker(
          name: "微笑.png",
          index: packageIconIndex,
          url: "assets/custom_face_resource/dada_default_emojis/"),
    );
    CustomEmojiFaceData customEmojiFaceData = CustomEmojiFaceData(
      name: "dada_default_emojis",
      list: emojiList,
      icon: "assets/custom_face_resource/dada_default_emojis/微笑.png",
    );

    List<CustomSticker> customStickers = [];
    List<String> customEmojiList = [];
    List<CustomEmojiEntity>? customEmojis = await ApiService().emojiList();
    if (customEmojis != null && customEmojis.isNotEmpty) {
      for (var i = 0; i < customEmojis.length; i++) {
        CustomEmojiEntity entity = customEmojis[i];
        String fileName = (entity.emoji ?? "").split("/").last;
        customEmojiList.add(fileName);
        customStickers
            .add(CustomSticker(name: fileName, index: i, url: entity.emoji));
      }
    }

    ///添加默认添加图片按钮
    customStickers.insert(
        0,
        const CustomSticker(
            name: "emoji_add.png",
            index: -1,
            url: "assets/custom_face_resource/custom_emojis/emoji_add.png"));
    customEmojiList.insert(0, "emoji_add.png");

    CustomStickerPackage customStickerPackage1 = CustomStickerPackage(
        name: "big_emoji",
        stickerList: customStickers,
        menuItem: const CustomSticker(
            name: 'chat_emoji_heart',
            index: -1,
            url:
                'assets/custom_face_resource/custom_emojis/chat_emoji_heart.png'));
    CustomEmojiFaceData customEmojiFaceData1 = CustomEmojiFaceData(
      name: "big_emoji",
      list: customEmojiList,
      icon: 'assets/custom_face_resource/custom_emojis/emoji_add.png',
    );
    _customEmojiStickerPackageList = [
      customStickerPackage,
      customStickerPackage1
    ];
    _customEmojiFaceDataList = [
      customEmojiFaceData,
      customEmojiFaceData1,
    ];
  }
}
