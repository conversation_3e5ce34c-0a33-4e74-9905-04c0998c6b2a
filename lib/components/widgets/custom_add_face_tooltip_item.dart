import 'package:dada/generated/assets.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/emoji_utils.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKItMessageList/tim_uikit_chat_history_message_list_item.dart';

class CustomAddFaceTooltipItem {
  static MessageToolTipItem build(
      {required V2TimMessage message,
      required Function() closeTooltip,
      required void Function() onClick}) {
    return MessageToolTipItem(
      label: "添加表情",
      id: "add_face",
      iconImageAsset: Assets.imagesChatAddIcon,
      onClick: () {
        ApiService().addEmoji(message.faceElem?.data ?? "").then((v) {
          if (v) {
            EmojiUtils().loadEmojiFiles().then((res) {
              closeTooltip.call();
              onClick.call();
            });
          }
        });
      },
    );
  }
}
