import 'package:dada/components/widgets/webView_page.dart';
import 'package:dada/pages/backpack/backpack_page.dart';
import 'package:dada/pages/chat/bottle/sweet_bottle_page.dart';
import 'package:dada/pages/chat/bottle/sweet_edit_page.dart';
import 'package:dada/pages/chat/bottle/sweet_list_page.dart';
import 'package:dada/pages/chat/custom_emoji/custom_emoji_page.dart';
import 'package:dada/pages/chat/search/chat_search_page.dart';
import 'package:dada/pages/chat/add/add_friend_send_page.dart';
import 'package:dada/pages/chat/create/group_chat_create_page.dart';
import 'package:dada/pages/chat/detail/chat_detail_page.dart';
import 'package:dada/pages/chat/setting/friend_custom_group_manager_page.dart';
import 'package:dada/pages/chat/together/msg_box_chat_page.dart';
import 'package:dada/pages/chat/world/world_chat_page.dart';
import 'package:dada/pages/chat_room/blackboard/chat_room_blackboard_edit_page.dart';
import 'package:dada/pages/chat_room/chat_room_page.dart';
import 'package:dada/pages/chat_room/create/chat_room_create_page.dart';
import 'package:dada/pages/chat_room/list/chat_room_list_page.dart';
import 'package:dada/pages/dynamic/detail/post_detail_page.dart';
import 'package:dada/pages/dynamic/dynamic_page.dart';
import 'package:dada/pages/dynamic/dynamic_say_page.dart';
import 'package:dada/pages/dynamic/list/mine_dynamic_list.dart';
import 'package:dada/pages/dynamic/message/dynamic_message_page.dart';
import 'package:dada/pages/dynamic/publish/dynamic_publish_page.dart';
import 'package:dada/pages/dynamic/resonance/dynamic_resonance_page.dart';
import 'package:dada/pages/dynamic/resonance/dynamic_resonance_list_page.dart';
import 'package:dada/pages/login/login_page.dart';
import 'package:dada/pages/login/register/pwd_forget_page.dart';
import 'package:dada/pages/login/register/select_label_page.dart';
import 'package:dada/pages/login/register_page.dart';
import 'package:dada/pages/login/secure_login_page.dart';
import 'package:dada/pages/login/welcome_page.dart';
import 'package:dada/pages/mine/auth/real_name_auth_page.dart';
import 'package:dada/pages/mine/auth/setup_password_page.dart';
import 'package:dada/pages/mine/auth/update_password_page.dart';
import 'package:dada/pages/mine/edit/avatar/select_avatar_page.dart';
import 'package:dada/pages/mine/invite/invite_friend_page.dart';
import 'package:dada/pages/mine/invite/invite_list_page.dart';
import 'package:dada/pages/mine/my_account/my_account_page.dart';
import 'package:dada/pages/mine/my_account/phone_change_page.dart';
import 'package:dada/pages/mine/my_privacy/location_limit_page.dart';
import 'package:dada/pages/mine/my_privacy/my_privacy_page.dart';
import 'package:dada/pages/mine/my_privacy/password_edit_page.dart';
import 'package:dada/pages/profile/card/other/user_other_card_add_page.dart';
import 'package:dada/pages/recharge/recharge_month_card_page.dart';
import 'package:dada/pages/settings/about/about_page.dart';
import 'package:dada/pages/settings/teen_mode/teen_mode_page.dart';
import 'package:dada/pages/settings/teen_mode/teen_mode_pwd_setting_page.dart';
import 'package:dada/pages/small_room/crystal_ball/crystal_ball_detail_page.dart';
import 'package:dada/pages/small_room/explore/explore_page.dart';
import 'package:dada/pages/store/store_page.dart';
import 'package:dada/pages/match/assemble/hall/match_assemble_place_hall_page.dart';
import 'package:dada/pages/match/assemble/match_assemble_place_page.dart';
import 'package:dada/pages/match/assemble/match_assemble_place_result_page.dart';
import 'package:dada/pages/match/assemble/team/match_team_chat_page.dart';
import 'package:dada/pages/match/dada/match_dada_list_page.dart';
import 'package:dada/pages/match/match_dada_page.dart';
import 'package:dada/pages/mine/audio_box/audio_box_page.dart';
import 'package:dada/pages/mine/edit/record/audio_record_page.dart';
import 'package:dada/pages/mine/edit/user_info_edit_page.dart';
import 'package:dada/pages/mine/info/user_info_controller.dart';
import 'package:dada/pages/profile/card/identity/user_identity_card_page.dart';
import 'package:dada/pages/profile/card/other/user_other_card_page.dart';
import 'package:dada/pages/profile/label/user_label_add_page.dart';
import 'package:dada/pages/profile/label/user_label_detail_page.dart';
import 'package:dada/pages/profile/user_profile_page.dart';
import 'package:dada/pages/recharge/recharge_detail/recharge_detail_page.dart';
import 'package:dada/pages/recharge/recharge_page.dart';
import 'package:dada/pages/search/search_room_page.dart';
import 'package:dada/pages/small_room/mail/small_room_mail_page.dart';
import 'package:dada/pages/settings/settings_page.dart';
import 'package:dada/pages/share/share_select_friend_page.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_page.dart';
import 'package:dada/pages/todo_together/publish/todo_together_publish_page.dart';
import 'package:dada/pages/todo_together/todo_together_history_page.dart';
import 'package:dada/pages/todo_together/todo_together_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';
import 'package:dada/pages/main/main_page.dart';
import 'package:dada/pages/splash/splash_page.dart';

import '../pages/settings/teen_mode/teen_mode_setting_page.dart';

const routeScheme = "dada:";

class GetRouter {
  static const main = "/main";
  static const splash = "/splash";
  static const welcome = "/welcome";
  static const login = "/login";
  static const register = "/register";
  static const userInfo = "/userInfo";
  static const settings = "/settings";
  static const editUserInfo = "/editUserInfo";
  static const audioRecord = "/audioRecord";
  static const audioBox = "/audioBox";
  static const userProfile = "/userProfile";
  static const userLabelDetail = "/userLabelDetail";
  static const userLabelAdd = "/userLabelAdd";
  static const userIdCard = "/userIdCard";
  static const otherCard = "/otherCard";
  static const addOtherCard = "/addOtherCard";
  static const postPublish = "/postPublish";
  static const message = "/message";
  static const mineDynamic = "/mineDynamic";
  static const postDetail = "/postDetail";
  static const matchDada = "/matchDada";
  static const matchAssemblePlace = "/matchAssemblePlace";
  static const matchDadaResult = "/matchDadaResult";
  static const matchTeamResult = "/matchTeamResult";
  static const assembleHall = "/assembleHall";
  static const teamDetail = "/teamDetail";
  static const chatDetail = "/chatDetail";
  static const createGroupChat = "/createGroupChat";
  static const chatSearch = "/chatSearch";
  static const chatBottle = "/chatBottle";
  static const chatSweetList = "/chatSweetList";
  static const chatSweetEdit = "/chatSweetEdit";
  static const friendCustomGroupManager = "/friendCustomGroupManager";
  static const addFriendSend = "/addFriendSend";
  static const smallRoomMail = "/smallRoomMail";
  static const chatRoomDetail = "/chatRoomDetail";
  static const createRoom = "/createRoom";
  static const editBlackboard = "/editBlackboard";
  static const chatRoomList = "/chatRoomList";
  static const shareSelectUser = "/shareSelectUser";
  static const searchRoom = "/searchRoom";
  static const recharge = "/recharge";
  static const rechargeMonthCard = "/rechargeMonthCard";
  static const rechargeDetail = "/rechargeDetail";
  static const store = "/store";
  static const backpack = "/backpack";
  static const dynamicSay = "/dynamicSay";
  static const dynamicList = "/dynamicList";
  static const dynamicResonance = "/dynamicResonance";
  static const dynamicResonanceList = "/dynamicResonanceList";
  static const myAccount = "/myAccount";
  static const inviteFriends = "/inviteFriends";
  static const myPrivacy = "/myPrivacy";
  static const locationLimit = "/locationLimit";
  static const passwordChange = "/passwordChange";
  static const phoneChange = "/phoneChange";
  static const realAuth = "/realAuth";
  static const worldChat = "/worldChat";
  static const crystalBallLifeDetail = "/crystalBallLifeDetail";
  static const explorePage = "/explorePage";
  static const inviteList = "/inviteList";
  static const selectAvatar = "/selectAvatar";
  static const secureLogin = "/secureLogin";
  static const setupPassword = "/setupPassword";
  static const updatePassword = "/updatePassword";
  static const forgetPassword = "/forgetPassword";
  static const webView = "/webView";
  static const about = "/about";
  static const teenMode = "/teenMode";
  static const teenModeSetting = "/teenModeSetting";
  static const teenModePwdSetting = "/teenModePwdSetting";
  static const registerAddLabel = "/registerAddLabel";
  static const todoTogether = "/todoTogether";
  static const todoTogetherHistory = "/todoTogetherHistory";
  static const todoTogetherPublish = "/todoTogetherPublish";
  static const todoTogetherDetail = "/todoTogetherDetail";
  static const todoTogetherMsgBox = "/todoTogetherMsgBox";
  static const customEmoji = "/customEmoji";

  static final List<GetPage> getPages = [
    GetPage(name: main, page: () => const MainPage()),
    GetPage(name: splash, page: () => const SplashPage()),
    GetPage(name: welcome, page: () => const WelcomePage()),
    GetPage(name: login, page: () => const LoginPage()),
    GetPage(name: register, page: () => const RegisterPage()),
    GetPage(name: userInfo, page: () => const UserInfoPage()),
    GetPage(name: settings, page: () => const SettingsPage()),
    GetPage(name: editUserInfo, page: () => const UserInfoEditPage()),
    GetPage(name: audioRecord, page: () => const AudioRecordPage()),
    GetPage(name: audioBox, page: () => const AudioBoxPage()),
    GetPage(name: userProfile, page: () => const UserProfilePage()),
    GetPage(name: userLabelAdd, page: () => const UserLabelAddPage()),
    GetPage(name: userLabelDetail, page: () => const UserLabelDetailPage()),
    GetPage(name: userIdCard, page: () => const UserIdentityCardPage()),
    GetPage(name: otherCard, page: () => const UserOtherCardPage()),
    GetPage(name: addOtherCard, page: () => const UserOtherCardAddPage()),
    GetPage(name: postPublish, page: () => const DynamicPublishPage()),
    GetPage(name: message, page: () => const DynamicMessagePage()),
    GetPage(name: mineDynamic, page: () => const MineDynamicListPage()),
    GetPage(name: postDetail, page: () => const PostDetailPage()),
    GetPage(name: matchDada, page: () => const MatchDadaPage()),
    GetPage(
        name: matchAssemblePlace, page: () => const MatchAssemblePlacePage()),
    GetPage(name: matchDadaResult, page: () => const MatchDadaListPage()),
    GetPage(
        name: matchTeamResult,
        page: () => const MatchAssemblePlaceResultPage()),
    GetPage(name: assembleHall, page: () => const MatchAssemblePlaceHallPage()),
    GetPage(name: teamDetail, page: () => const MatchTeamChatPage()),
    GetPage(name: chatDetail, page: () => const ChatDetailPage()),
    GetPage(name: createGroupChat, page: () => const GroupChatCreatePage()),
    GetPage(name: chatSearch, page: () => const ChatSearchPage()),
    GetPage(name: chatBottle, page: () => const SweetBottlePage()),
    GetPage(name: chatSweetEdit, page: () => const SweetEditPage()),
    GetPage(name: chatSweetList, page: () => const SweetListPage()),
    GetPage(
        name: friendCustomGroupManager,
        page: () => const FriendChangeCustomGroup()),
    GetPage(name: addFriendSend, page: () => const AddFriendSendPage()),
    GetPage(name: smallRoomMail, page: () => const SmallRoomMailPage()),
    GetPage(name: chatRoomDetail, page: () => const ChatRoomPage()),
    GetPage(name: createRoom, page: () => const ChatRoomCreatePage()),
    GetPage(
        name: editBlackboard, page: () => const ChatRoomBlackboardEditPage()),
    GetPage(name: chatRoomList, page: () => const ChatRoomListPage()),
    GetPage(name: shareSelectUser, page: () => const ShareSelectFriendPage()),
    GetPage(name: searchRoom, page: () => const SearchRoomPage()),
    GetPage(name: recharge, page: () => const RechargePage()),
    GetPage(name: rechargeMonthCard, page: () => RechargeMonthCardPage()),
    GetPage(name: rechargeDetail, page: () => const RechargeDetailPage()),
    GetPage(name: store, page: () => const StorePage()),
    GetPage(name: backpack, page: () => const BackpackPage()),
    GetPage(name: dynamicSay, page: () => const DynamicSayPage()),
    GetPage(name: dynamicList, page: () => const DynamicPage()),
    GetPage(name: dynamicResonance, page: () => const DynamicResonancePage()),
    GetPage(
        name: dynamicResonanceList,
        page: () => const DynamicResonateListPage()),
    GetPage(name: myAccount, page: () => const MyAccountPage()),
    GetPage(name: myPrivacy, page: () => const MyPrivacyPage()),
    GetPage(name: locationLimit, page: () => LocationLimitPage()),
    GetPage(name: passwordChange, page: () => const PasswordEditPage()),
    GetPage(name: phoneChange, page: () => const PhoneChangePage()),
    GetPage(name: realAuth, page: () => const RealNameAuthPage()),
    GetPage(name: worldChat, page: () => const WorldChatPage()),
    GetPage(
        name: crystalBallLifeDetail, page: () => const CrystalBallDetailPage()),
    GetPage(
        name: dynamicResonanceList,
        page: () => const DynamicResonateListPage()),
    GetPage(name: inviteFriends, page: () => const InviteFriendPage()),
    GetPage(name: myAccount, page: () => const MyAccountPage()),
    GetPage(name: myPrivacy, page: () => const MyPrivacyPage()),
    GetPage(name: explorePage, page: () => const ExplorePage()),
    GetPage(name: inviteList, page: () => const InviteListPage()),
    GetPage(name: selectAvatar, page: () => const SelectAvatarPage()),
    GetPage(name: secureLogin, page: () => const SecureLoginPage()),
    GetPage(name: setupPassword, page: () => const SetupPassWordPage()),
    GetPage(name: updatePassword, page: () => const UpdatePassWordPage()),
    GetPage(name: forgetPassword, page: () => const PwdForgetPage()),
    GetPage(name: webView, page: () => const WebViewPage()),
    GetPage(name: about, page: () => const AboutPage()),
    GetPage(name: teenMode, page: () => const TeenModePage()),
    GetPage(name: teenModeSetting, page: () => const TeenModeSettingPage()),
    GetPage(
        name: teenModePwdSetting, page: () => const TeenModePwdSettingPage()),
    GetPage(name: registerAddLabel, page: () => const SelectLabelPage()),
    GetPage(name: todoTogether, page: () => const TodoTogetherPage()),
    GetPage(
        name: todoTogetherHistory, page: () => const TodoTogetherHistoryPage()),
    GetPage(
        name: todoTogetherPublish, page: () => const TodoTogetherPublishPage()),
    GetPage(
        name: todoTogetherDetail, page: () => const TodoTogetherDetailPage()),
    GetPage(name: todoTogetherMsgBox, page: () => const MsgBoxChatPage()),
    GetPage(name: customEmoji, page: () => const CustomEmojiPage()),
  ];
}
