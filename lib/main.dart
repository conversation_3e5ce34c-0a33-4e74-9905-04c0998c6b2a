import 'dart:async';
import 'dart:io';
import 'package:dada/configs/app_config.dart';
import 'package:dada/plugin/rebuild/rebuild_inspector.dart';
import 'package:dada/services/ad/ad_plugin_manager.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dada/app.dart';
import 'package:dada/global.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:flutter_ume/flutter_ume.dart'; // UME framework
import 'package:flutter_ume_kit_device/flutter_ume_kit_device.dart'; // Device info
import 'package:flutter_ume_kit_dio/flutter_ume_kit_dio.dart'; // Dio Inspector
import 'package:flutter_ume_kit_perf/flutter_ume_kit_perf.dart'; // Performance kits
import 'package:flutter_ume_kit_console/flutter_ume_kit_console.dart'; // Performance kits
import 'package:flutter_ume_kit_ui/flutter_ume_kit_ui.dart';
import 'package:flutter_ume_kit_mock/flutter_ume_kit_mock.dart';
import 'package:dada/services/network/http_utils.dart';
import 'package:dada/utils/log.dart'; //
import 'package:flutter_native_splash/flutter_native_splash.dart';

void main() async {
  runZonedGuarded(() {
    ///Widget binding
    WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
    FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

    SystemChrome.setPreferredOrientations(
      [
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ],
    );

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // 状态栏背景颜色
        statusBarIconBrightness: Brightness.dark, // 设置状态栏图标亮度
      ),
    );

    Global.init().then((e) {
      bool isLaunchFinished = false;

      ///ad plugin
      AdPluginManager.instance;
      FlutterGromoreAds.initAd(
              Platform.isIOS
                  ? AppConfig.gromoreAdsAppId_iOS
                  : AppConfig.gromoreAdsAppId_Android,
              useMediation: true,
              themeStatus: 1)
          .then((v) {
        assert(() {
          PluginManager.instance // Register plugin kits
            ..register(const WidgetInfoInspector())
            ..register(DioInspector(dio: HttpUtil.dio))
            ..register(MockInspector(
              dio: HttpUtil.dio,
              feishuAppId: "cli_a3310594f0bad00d",
              feishuAppSecret: "ZJXjOKp4qH4wKuRthqp3xeIv2Uak2m2W",
            ))
            ..register(const WidgetDetailInspector())
            ..register(const ColorSucker())
            ..register(AlignRuler())
            ..register(const TouchIndicator()) // New feature
            ..register(Performance())
            ..register(Console())
            ..register(const ColorPicker()) // New feature
            ..register(const MemoryInfoPage())
            ..register(CpuInfoPage())
            ..register(RebuildInspector())
            ..register(const DeviceInfoPanel());

          runApp(const UMEWidget(enable: true, child: MyApp()));

          ///错误收集
          FlutterError.onError = (FlutterErrorDetails details) async {
            Log.d(details);
          };

          isLaunchFinished = true;
          return true;
        }());

        if (isLaunchFinished) {
          return;
        }
        runApp(const MyApp());
      });
    });
  }, (error, stack) {
    if (kDebugMode) {
      print(error);
      print(stack);
    }
  });
}
