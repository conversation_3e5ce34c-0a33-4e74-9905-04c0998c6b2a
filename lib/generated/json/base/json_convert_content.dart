// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:dada/model/account_balance_entity.dart';
import 'package:dada/model/audio_box_list_item_entity.dart';
import 'package:dada/model/banner_entity.dart';
import 'package:dada/model/chat_group_info_entity.dart';
import 'package:dada/model/chat_group_label_entity.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/chat_room_history_list_item_entity.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/chat_room_list_result_entity.dart';
import 'package:dada/model/chat_room_online_member_entity.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/model/chat_searched_result_entity.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/model/comment_list_entity.dart';
import 'package:dada/model/comment_reply_item_entity.dart';
import 'package:dada/model/comment_reply_list_entity.dart';
import 'package:dada/model/contact_group_list_item_entity.dart';
import 'package:dada/model/crystal_ball_life_record_entity.dart';
import 'package:dada/model/custom_emoji_entity.dart';
import 'package:dada/model/dada_match_result_entity.dart';
import 'package:dada/model/dynamic_message_item_entity.dart';
import 'package:dada/model/dynamic_message_list_entity.dart';
import 'package:dada/model/dynamic_resonance_entity.dart';
import 'package:dada/model/friend_group_entity.dart';
import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/global_notice_entity.dart';
import 'package:dada/model/goods_entity.dart';
import 'package:dada/model/group_chat_application_entity.dart';
import 'package:dada/model/group_list_item_entity.dart';
import 'package:dada/model/home_list_item_entity.dart';
import 'package:dada/model/invite_list_entity.dart';
import 'package:dada/model/invite_play_entity.dart';
import 'package:dada/model/match_assemble_place_list_item_entity.dart';
import 'package:dada/model/match_dada_list_item_entity.dart';
import 'package:dada/model/match_team_list_entity.dart';
import 'package:dada/model/match_team_member_entity.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/model/pay_info.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/model/post_list_entity.dart';
import 'package:dada/model/prop_entity.dart';
import 'package:dada/model/recharge_detail_list_item_entity.dart';
import 'package:dada/model/region_limit_entity.dart';
import 'package:dada/model/reward_item_entity.dart';
import 'package:dada/model/search_room_history_result_entity.dart';
import 'package:dada/model/sign_in_item_entity.dart';
import 'package:dada/model/sign_in_list_item_entity.dart';
import 'package:dada/model/simple_user_info_entity.dart';
import 'package:dada/model/small_room_barrage_item_entity.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';
import 'package:dada/model/small_room_detail_info_entity.dart';
import 'package:dada/model/small_room_explore_info_entity.dart';
import 'package:dada/model/small_room_mail_list_item_entity.dart';
import 'package:dada/model/small_room_mail_unread_msg_entity.dart';
import 'package:dada/model/small_room_see_me_entity.dart';
import 'package:dada/model/small_room_task_entity.dart';
import 'package:dada/model/store_goods_item_entity.dart';
import 'package:dada/model/sweet_bottle_entity.dart';
import 'package:dada/model/system_config_entity.dart';
import 'package:dada/model/tab_bar_item_entity.dart';
import 'package:dada/model/teen_mode_status_entity.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/model/todo_together_list_entity.dart';
import 'package:dada/model/topic_item_entity.dart';
import 'package:dada/model/trtc_custom_msg_entity.dart';
import 'package:dada/model/upload_file_entity.dart';
import 'package:dada/model/use_prop_result_entity.dart';
import 'package:dada/model/user_avatar_entity.dart';
import 'package:dada/model/user_card_box_entity.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/model/user_label_entity.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);
extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => _asT<T>(e, enumConvert: enumConvert))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) =>
      _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError(
            '$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<AccountBalanceEntity>[] is M) {
      return data.map<AccountBalanceEntity>((Map<String, dynamic> e) =>
          AccountBalanceEntity.fromJson(e)).toList() as M;
    }
    if (<AudioBoxListItemEntity>[] is M) {
      return data.map<AudioBoxListItemEntity>((Map<String, dynamic> e) =>
          AudioBoxListItemEntity.fromJson(e)).toList() as M;
    }
    if (<BannerEntity>[] is M) {
      return data.map<BannerEntity>((Map<String, dynamic> e) =>
          BannerEntity.fromJson(e)).toList() as M;
    }
    if (<ChatGroupInfoEntity>[] is M) {
      return data.map<ChatGroupInfoEntity>((Map<String, dynamic> e) =>
          ChatGroupInfoEntity.fromJson(e)).toList() as M;
    }
    if (<ChatGroupLabelEntity>[] is M) {
      return data.map<ChatGroupLabelEntity>((Map<String, dynamic> e) =>
          ChatGroupLabelEntity.fromJson(e)).toList() as M;
    }
    if (<ChatImCustomMsgEntity>[] is M) {
      return data.map<ChatImCustomMsgEntity>((Map<String, dynamic> e) =>
          ChatImCustomMsgEntity.fromJson(e)).toList() as M;
    }
    if (<ChatRoomHistoryListItemEntity>[] is M) {
      return data.map<ChatRoomHistoryListItemEntity>((Map<String, dynamic> e) =>
          ChatRoomHistoryListItemEntity.fromJson(e)).toList() as M;
    }
    if (<ChatRoomInfoEntity>[] is M) {
      return data.map<ChatRoomInfoEntity>((Map<String, dynamic> e) =>
          ChatRoomInfoEntity.fromJson(e)).toList() as M;
    }
    if (<ChatRoomListResultEntity>[] is M) {
      return data.map<ChatRoomListResultEntity>((Map<String, dynamic> e) =>
          ChatRoomListResultEntity.fromJson(e)).toList() as M;
    }
    if (<ChatRoomOnlineMemberEntity>[] is M) {
      return data.map<ChatRoomOnlineMemberEntity>((Map<String, dynamic> e) =>
          ChatRoomOnlineMemberEntity.fromJson(e)).toList() as M;
    }
    if (<ChatRoomSeatInfoEntity>[] is M) {
      return data.map<ChatRoomSeatInfoEntity>((Map<String, dynamic> e) =>
          ChatRoomSeatInfoEntity.fromJson(e)).toList() as M;
    }
    if (<ChatRoomSeatInfoUser>[] is M) {
      return data.map<ChatRoomSeatInfoUser>((Map<String, dynamic> e) =>
          ChatRoomSeatInfoUser.fromJson(e)).toList() as M;
    }
    if (<ChatSearchedResultEntity>[] is M) {
      return data.map<ChatSearchedResultEntity>((Map<String, dynamic> e) =>
          ChatSearchedResultEntity.fromJson(e)).toList() as M;
    }
    if (<CommentItemEntity>[] is M) {
      return data.map<CommentItemEntity>((Map<String, dynamic> e) =>
          CommentItemEntity.fromJson(e)).toList() as M;
    }
    if (<CommentListEntity>[] is M) {
      return data.map<CommentListEntity>((Map<String, dynamic> e) =>
          CommentListEntity.fromJson(e)).toList() as M;
    }
    if (<CommentReplyItemEntity>[] is M) {
      return data.map<CommentReplyItemEntity>((Map<String, dynamic> e) =>
          CommentReplyItemEntity.fromJson(e)).toList() as M;
    }
    if (<CommentReplyListEntity>[] is M) {
      return data.map<CommentReplyListEntity>((Map<String, dynamic> e) =>
          CommentReplyListEntity.fromJson(e)).toList() as M;
    }
    if (<ContactGroupListItemEntity>[] is M) {
      return data.map<ContactGroupListItemEntity>((Map<String, dynamic> e) =>
          ContactGroupListItemEntity.fromJson(e)).toList() as M;
    }
    if (<CrystalBallLifeRecordEntity>[] is M) {
      return data.map<CrystalBallLifeRecordEntity>((Map<String, dynamic> e) =>
          CrystalBallLifeRecordEntity.fromJson(e)).toList() as M;
    }
    if (<CustomEmojiEntity>[] is M) {
      return data.map<CustomEmojiEntity>((Map<String, dynamic> e) =>
          CustomEmojiEntity.fromJson(e)).toList() as M;
    }
    if (<DadaMatchResultEntity>[] is M) {
      return data.map<DadaMatchResultEntity>((Map<String, dynamic> e) =>
          DadaMatchResultEntity.fromJson(e)).toList() as M;
    }
    if (<DynamicMessageItemEntity>[] is M) {
      return data.map<DynamicMessageItemEntity>((Map<String, dynamic> e) =>
          DynamicMessageItemEntity.fromJson(e)).toList() as M;
    }
    if (<DynamicMessageListEntity>[] is M) {
      return data.map<DynamicMessageListEntity>((Map<String, dynamic> e) =>
          DynamicMessageListEntity.fromJson(e)).toList() as M;
    }
    if (<DynamicResonanceEntity>[] is M) {
      return data.map<DynamicResonanceEntity>((Map<String, dynamic> e) =>
          DynamicResonanceEntity.fromJson(e)).toList() as M;
    }
    if (<FriendGroupEntity>[] is M) {
      return data.map<FriendGroupEntity>((Map<String, dynamic> e) =>
          FriendGroupEntity.fromJson(e)).toList() as M;
    }
    if (<FriendSubGroupEntity>[] is M) {
      return data.map<FriendSubGroupEntity>((Map<String, dynamic> e) =>
          FriendSubGroupEntity.fromJson(e)).toList() as M;
    }
    if (<FriendUserInfoEntity>[] is M) {
      return data.map<FriendUserInfoEntity>((Map<String, dynamic> e) =>
          FriendUserInfoEntity.fromJson(e)).toList() as M;
    }
    if (<GlobalNoticeEntity>[] is M) {
      return data.map<GlobalNoticeEntity>((Map<String, dynamic> e) =>
          GlobalNoticeEntity.fromJson(e)).toList() as M;
    }
    if (<GoodsEntity>[] is M) {
      return data.map<GoodsEntity>((Map<String, dynamic> e) =>
          GoodsEntity.fromJson(e)).toList() as M;
    }
    if (<GoodsRewardEntity>[] is M) {
      return data.map<GoodsRewardEntity>((Map<String, dynamic> e) =>
          GoodsRewardEntity.fromJson(e)).toList() as M;
    }
    if (<GroupChatApplicationEntity>[] is M) {
      return data.map<GroupChatApplicationEntity>((Map<String, dynamic> e) =>
          GroupChatApplicationEntity.fromJson(e)).toList() as M;
    }
    if (<GroupListItemEntity>[] is M) {
      return data.map<GroupListItemEntity>((Map<String, dynamic> e) =>
          GroupListItemEntity.fromJson(e)).toList() as M;
    }
    if (<HomeListItemEntity>[] is M) {
      return data.map<HomeListItemEntity>((Map<String, dynamic> e) =>
          HomeListItemEntity.fromJson(e)).toList() as M;
    }
    if (<InviteListEntity>[] is M) {
      return data.map<InviteListEntity>((Map<String, dynamic> e) =>
          InviteListEntity.fromJson(e)).toList() as M;
    }
    if (<InviteListInviteRecordDetailsVos>[] is M) {
      return data.map<InviteListInviteRecordDetailsVos>((
          Map<String, dynamic> e) =>
          InviteListInviteRecordDetailsVos.fromJson(e)).toList() as M;
    }
    if (<InviteRecordRewardItem>[] is M) {
      return data.map<InviteRecordRewardItem>((Map<String, dynamic> e) =>
          InviteRecordRewardItem.fromJson(e)).toList() as M;
    }
    if (<InvitePlayEntity>[] is M) {
      return data.map<InvitePlayEntity>((Map<String, dynamic> e) =>
          InvitePlayEntity.fromJson(e)).toList() as M;
    }
    if (<MatchAssemblePlaceListItemEntity>[] is M) {
      return data.map<MatchAssemblePlaceListItemEntity>((
          Map<String, dynamic> e) =>
          MatchAssemblePlaceListItemEntity.fromJson(e)).toList() as M;
    }
    if (<MatchDadaListItemEntity>[] is M) {
      return data.map<MatchDadaListItemEntity>((Map<String, dynamic> e) =>
          MatchDadaListItemEntity.fromJson(e)).toList() as M;
    }
    if (<MatchTeamListEntity>[] is M) {
      return data.map<MatchTeamListEntity>((Map<String, dynamic> e) =>
          MatchTeamListEntity.fromJson(e)).toList() as M;
    }
    if (<MatchTeamMemberEntity>[] is M) {
      return data.map<MatchTeamMemberEntity>((Map<String, dynamic> e) =>
          MatchTeamMemberEntity.fromJson(e)).toList() as M;
    }
    if (<MatchTeamResultEntity>[] is M) {
      return data.map<MatchTeamResultEntity>((Map<String, dynamic> e) =>
          MatchTeamResultEntity.fromJson(e)).toList() as M;
    }
    if (<PayInfo>[] is M) {
      return data.map<PayInfo>((Map<String, dynamic> e) => PayInfo.fromJson(e))
          .toList() as M;
    }
    if (<PostEntity>[] is M) {
      return data.map<PostEntity>((Map<String, dynamic> e) =>
          PostEntity.fromJson(e)).toList() as M;
    }
    if (<PostListEntity>[] is M) {
      return data.map<PostListEntity>((Map<String, dynamic> e) =>
          PostListEntity.fromJson(e)).toList() as M;
    }
    if (<PropEntity>[] is M) {
      return data.map<PropEntity>((Map<String, dynamic> e) =>
          PropEntity.fromJson(e)).toList() as M;
    }
    if (<PropDressColorEntity>[] is M) {
      return data.map<PropDressColorEntity>((Map<String, dynamic> e) =>
          PropDressColorEntity.fromJson(e)).toList() as M;
    }
    if (<RechargeDetailListItemEntity>[] is M) {
      return data.map<RechargeDetailListItemEntity>((Map<String, dynamic> e) =>
          RechargeDetailListItemEntity.fromJson(e)).toList() as M;
    }
    if (<RegionLimitEntity>[] is M) {
      return data.map<RegionLimitEntity>((Map<String, dynamic> e) =>
          RegionLimitEntity.fromJson(e)).toList() as M;
    }
    if (<RewardItemEntity>[] is M) {
      return data.map<RewardItemEntity>((Map<String, dynamic> e) =>
          RewardItemEntity.fromJson(e)).toList() as M;
    }
    if (<SearchRoomHistoryResultEntity>[] is M) {
      return data.map<SearchRoomHistoryResultEntity>((Map<String, dynamic> e) =>
          SearchRoomHistoryResultEntity.fromJson(e)).toList() as M;
    }
    if (<SearchRoomHistoryResultTopicBoosterList>[] is M) {
      return data.map<SearchRoomHistoryResultTopicBoosterList>((
          Map<String, dynamic> e) =>
          SearchRoomHistoryResultTopicBoosterList.fromJson(e)).toList() as M;
    }
    if (<SignInItemEntity>[] is M) {
      return data.map<SignInItemEntity>((Map<String, dynamic> e) =>
          SignInItemEntity.fromJson(e)).toList() as M;
    }
    if (<SignInListItemEntity>[] is M) {
      return data.map<SignInListItemEntity>((Map<String, dynamic> e) =>
          SignInListItemEntity.fromJson(e)).toList() as M;
    }
    if (<SimpleUserInfoEntity>[] is M) {
      return data.map<SimpleUserInfoEntity>((Map<String, dynamic> e) =>
          SimpleUserInfoEntity.fromJson(e)).toList() as M;
    }
    if (<SmallRoomBarrageItemEntity>[] is M) {
      return data.map<SmallRoomBarrageItemEntity>((Map<String, dynamic> e) =>
          SmallRoomBarrageItemEntity.fromJson(e)).toList() as M;
    }
    if (<SmallRoomBubbleWordEntity>[] is M) {
      return data.map<SmallRoomBubbleWordEntity>((Map<String, dynamic> e) =>
          SmallRoomBubbleWordEntity.fromJson(e)).toList() as M;
    }
    if (<SmallRoomDetailInfoEntity>[] is M) {
      return data.map<SmallRoomDetailInfoEntity>((Map<String, dynamic> e) =>
          SmallRoomDetailInfoEntity.fromJson(e)).toList() as M;
    }
    if (<SmallRoomInfoRoom>[] is M) {
      return data.map<SmallRoomInfoRoom>((Map<String, dynamic> e) =>
          SmallRoomInfoRoom.fromJson(e)).toList() as M;
    }
    if (<SmallRoomExploreInfoEntity>[] is M) {
      return data.map<SmallRoomExploreInfoEntity>((Map<String, dynamic> e) =>
          SmallRoomExploreInfoEntity.fromJson(e)).toList() as M;
    }
    if (<SmallRoomExploreInfoExploreProp1>[] is M) {
      return data.map<SmallRoomExploreInfoExploreProp1>((
          Map<String, dynamic> e) =>
          SmallRoomExploreInfoExploreProp1.fromJson(e)).toList() as M;
    }
    if (<SmallRoomExploreInfoExploreProp2>[] is M) {
      return data.map<SmallRoomExploreInfoExploreProp2>((
          Map<String, dynamic> e) =>
          SmallRoomExploreInfoExploreProp2.fromJson(e)).toList() as M;
    }
    if (<SmallRoomMailListItemEntity>[] is M) {
      return data.map<SmallRoomMailListItemEntity>((Map<String, dynamic> e) =>
          SmallRoomMailListItemEntity.fromJson(e)).toList() as M;
    }
    if (<SmallRoomMailUnreadMsgEntity>[] is M) {
      return data.map<SmallRoomMailUnreadMsgEntity>((Map<String, dynamic> e) =>
          SmallRoomMailUnreadMsgEntity.fromJson(e)).toList() as M;
    }
    if (<SmallRoomSeeMeEntity>[] is M) {
      return data.map<SmallRoomSeeMeEntity>((Map<String, dynamic> e) =>
          SmallRoomSeeMeEntity.fromJson(e)).toList() as M;
    }
    if (<SmallRoomTaskEntity>[] is M) {
      return data.map<SmallRoomTaskEntity>((Map<String, dynamic> e) =>
          SmallRoomTaskEntity.fromJson(e)).toList() as M;
    }
    if (<StoreGoodsItemEntity>[] is M) {
      return data.map<StoreGoodsItemEntity>((Map<String, dynamic> e) =>
          StoreGoodsItemEntity.fromJson(e)).toList() as M;
    }
    if (<SweetBottleEntity>[] is M) {
      return data.map<SweetBottleEntity>((Map<String, dynamic> e) =>
          SweetBottleEntity.fromJson(e)).toList() as M;
    }
    if (<SystemConfigEntity>[] is M) {
      return data.map<SystemConfigEntity>((Map<String, dynamic> e) =>
          SystemConfigEntity.fromJson(e)).toList() as M;
    }
    if (<TabBarItemEntity>[] is M) {
      return data.map<TabBarItemEntity>((Map<String, dynamic> e) =>
          TabBarItemEntity.fromJson(e)).toList() as M;
    }
    if (<TeenModeStatusEntity>[] is M) {
      return data.map<TeenModeStatusEntity>((Map<String, dynamic> e) =>
          TeenModeStatusEntity.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailEntity>[] is M) {
      return data.map<TodoTogetherDetailEntity>((Map<String, dynamic> e) =>
          TodoTogetherDetailEntity.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage6>[] is M) {
      return data.map<TodoTogetherDetailStage6>((Map<String, dynamic> e) =>
          TodoTogetherDetailStage6.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage6OtherEndMsg>[] is M) {
      return data.map<TodoTogetherDetailStage6OtherEndMsg>((
          Map<String, dynamic> e) =>
          TodoTogetherDetailStage6OtherEndMsg.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage8>[] is M) {
      return data.map<TodoTogetherDetailStage8>((Map<String, dynamic> e) =>
          TodoTogetherDetailStage8.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage3>[] is M) {
      return data.map<TodoTogetherDetailStage3>((Map<String, dynamic> e) =>
          TodoTogetherDetailStage3.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage2>[] is M) {
      return data.map<TodoTogetherDetailStage2>((Map<String, dynamic> e) =>
          TodoTogetherDetailStage2.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage5>[] is M) {
      return data.map<TodoTogetherDetailStage5>((Map<String, dynamic> e) =>
          TodoTogetherDetailStage5.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage5MyEndMsg>[] is M) {
      return data.map<TodoTogetherDetailStage5MyEndMsg>((
          Map<String, dynamic> e) =>
          TodoTogetherDetailStage5MyEndMsg.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage4>[] is M) {
      return data.map<TodoTogetherDetailStage4>((Map<String, dynamic> e) =>
          TodoTogetherDetailStage4.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherDetailStage1>[] is M) {
      return data.map<TodoTogetherDetailStage1>((Map<String, dynamic> e) =>
          TodoTogetherDetailStage1.fromJson(e)).toList() as M;
    }
    if (<TodoTogetherListEntity>[] is M) {
      return data.map<TodoTogetherListEntity>((Map<String, dynamic> e) =>
          TodoTogetherListEntity.fromJson(e)).toList() as M;
    }
    if (<TopicItemEntity>[] is M) {
      return data.map<TopicItemEntity>((Map<String, dynamic> e) =>
          TopicItemEntity.fromJson(e)).toList() as M;
    }
    if (<TRTCCustomMsgEntity>[] is M) {
      return data.map<TRTCCustomMsgEntity>((Map<String, dynamic> e) =>
          TRTCCustomMsgEntity.fromJson(e)).toList() as M;
    }
    if (<UploadFileEntity>[] is M) {
      return data.map<UploadFileEntity>((Map<String, dynamic> e) =>
          UploadFileEntity.fromJson(e)).toList() as M;
    }
    if (<UsePropResultEntity>[] is M) {
      return data.map<UsePropResultEntity>((Map<String, dynamic> e) =>
          UsePropResultEntity.fromJson(e)).toList() as M;
    }
    if (<UserAvatarEntity>[] is M) {
      return data.map<UserAvatarEntity>((Map<String, dynamic> e) =>
          UserAvatarEntity.fromJson(e)).toList() as M;
    }
    if (<UserCardBoxEntity>[] is M) {
      return data.map<UserCardBoxEntity>((Map<String, dynamic> e) =>
          UserCardBoxEntity.fromJson(e)).toList() as M;
    }
    if (<UserIdCardEntity>[] is M) {
      return data.map<UserIdCardEntity>((Map<String, dynamic> e) =>
          UserIdCardEntity.fromJson(e)).toList() as M;
    }
    if (<UserInfoEntity>[] is M) {
      return data.map<UserInfoEntity>((Map<String, dynamic> e) =>
          UserInfoEntity.fromJson(e)).toList() as M;
    }
    if (<UserLabelEntity>[] is M) {
      return data.map<UserLabelEntity>((Map<String, dynamic> e) =>
          UserLabelEntity.fromJson(e)).toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(
          json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (AccountBalanceEntity).toString(): AccountBalanceEntity.fromJson,
    (AudioBoxListItemEntity).toString(): AudioBoxListItemEntity.fromJson,
    (BannerEntity).toString(): BannerEntity.fromJson,
    (ChatGroupInfoEntity).toString(): ChatGroupInfoEntity.fromJson,
    (ChatGroupLabelEntity).toString(): ChatGroupLabelEntity.fromJson,
    (ChatImCustomMsgEntity).toString(): ChatImCustomMsgEntity.fromJson,
    (ChatRoomHistoryListItemEntity).toString(): ChatRoomHistoryListItemEntity
        .fromJson,
    (ChatRoomInfoEntity).toString(): ChatRoomInfoEntity.fromJson,
    (ChatRoomListResultEntity).toString(): ChatRoomListResultEntity.fromJson,
    (ChatRoomOnlineMemberEntity).toString(): ChatRoomOnlineMemberEntity
        .fromJson,
    (ChatRoomSeatInfoEntity).toString(): ChatRoomSeatInfoEntity.fromJson,
    (ChatRoomSeatInfoUser).toString(): ChatRoomSeatInfoUser.fromJson,
    (ChatSearchedResultEntity).toString(): ChatSearchedResultEntity.fromJson,
    (CommentItemEntity).toString(): CommentItemEntity.fromJson,
    (CommentListEntity).toString(): CommentListEntity.fromJson,
    (CommentReplyItemEntity).toString(): CommentReplyItemEntity.fromJson,
    (CommentReplyListEntity).toString(): CommentReplyListEntity.fromJson,
    (ContactGroupListItemEntity).toString(): ContactGroupListItemEntity
        .fromJson,
    (CrystalBallLifeRecordEntity).toString(): CrystalBallLifeRecordEntity
        .fromJson,
    (CustomEmojiEntity).toString(): CustomEmojiEntity.fromJson,
    (DadaMatchResultEntity).toString(): DadaMatchResultEntity.fromJson,
    (DynamicMessageItemEntity).toString(): DynamicMessageItemEntity.fromJson,
    (DynamicMessageListEntity).toString(): DynamicMessageListEntity.fromJson,
    (DynamicResonanceEntity).toString(): DynamicResonanceEntity.fromJson,
    (FriendGroupEntity).toString(): FriendGroupEntity.fromJson,
    (FriendSubGroupEntity).toString(): FriendSubGroupEntity.fromJson,
    (FriendUserInfoEntity).toString(): FriendUserInfoEntity.fromJson,
    (GlobalNoticeEntity).toString(): GlobalNoticeEntity.fromJson,
    (GoodsEntity).toString(): GoodsEntity.fromJson,
    (GoodsRewardEntity).toString(): GoodsRewardEntity.fromJson,
    (GroupChatApplicationEntity).toString(): GroupChatApplicationEntity
        .fromJson,
    (GroupListItemEntity).toString(): GroupListItemEntity.fromJson,
    (HomeListItemEntity).toString(): HomeListItemEntity.fromJson,
    (InviteListEntity).toString(): InviteListEntity.fromJson,
    (InviteListInviteRecordDetailsVos)
        .toString(): InviteListInviteRecordDetailsVos.fromJson,
    (InviteRecordRewardItem).toString(): InviteRecordRewardItem.fromJson,
    (InvitePlayEntity).toString(): InvitePlayEntity.fromJson,
    (MatchAssemblePlaceListItemEntity)
        .toString(): MatchAssemblePlaceListItemEntity.fromJson,
    (MatchDadaListItemEntity).toString(): MatchDadaListItemEntity.fromJson,
    (MatchTeamListEntity).toString(): MatchTeamListEntity.fromJson,
    (MatchTeamMemberEntity).toString(): MatchTeamMemberEntity.fromJson,
    (MatchTeamResultEntity).toString(): MatchTeamResultEntity.fromJson,
    (PayInfo).toString(): PayInfo.fromJson,
    (PostEntity).toString(): PostEntity.fromJson,
    (PostListEntity).toString(): PostListEntity.fromJson,
    (PropEntity).toString(): PropEntity.fromJson,
    (PropDressColorEntity).toString(): PropDressColorEntity.fromJson,
    (RechargeDetailListItemEntity).toString(): RechargeDetailListItemEntity
        .fromJson,
    (RegionLimitEntity).toString(): RegionLimitEntity.fromJson,
    (RewardItemEntity).toString(): RewardItemEntity.fromJson,
    (SearchRoomHistoryResultEntity).toString(): SearchRoomHistoryResultEntity
        .fromJson,
    (SearchRoomHistoryResultTopicBoosterList)
        .toString(): SearchRoomHistoryResultTopicBoosterList.fromJson,
    (SignInItemEntity).toString(): SignInItemEntity.fromJson,
    (SignInListItemEntity).toString(): SignInListItemEntity.fromJson,
    (SimpleUserInfoEntity).toString(): SimpleUserInfoEntity.fromJson,
    (SmallRoomBarrageItemEntity).toString(): SmallRoomBarrageItemEntity
        .fromJson,
    (SmallRoomBubbleWordEntity).toString(): SmallRoomBubbleWordEntity.fromJson,
    (SmallRoomDetailInfoEntity).toString(): SmallRoomDetailInfoEntity.fromJson,
    (SmallRoomInfoRoom).toString(): SmallRoomInfoRoom.fromJson,
    (SmallRoomExploreInfoEntity).toString(): SmallRoomExploreInfoEntity
        .fromJson,
    (SmallRoomExploreInfoExploreProp1)
        .toString(): SmallRoomExploreInfoExploreProp1.fromJson,
    (SmallRoomExploreInfoExploreProp2)
        .toString(): SmallRoomExploreInfoExploreProp2.fromJson,
    (SmallRoomMailListItemEntity).toString(): SmallRoomMailListItemEntity
        .fromJson,
    (SmallRoomMailUnreadMsgEntity).toString(): SmallRoomMailUnreadMsgEntity
        .fromJson,
    (SmallRoomSeeMeEntity).toString(): SmallRoomSeeMeEntity.fromJson,
    (SmallRoomTaskEntity).toString(): SmallRoomTaskEntity.fromJson,
    (StoreGoodsItemEntity).toString(): StoreGoodsItemEntity.fromJson,
    (SweetBottleEntity).toString(): SweetBottleEntity.fromJson,
    (SystemConfigEntity).toString(): SystemConfigEntity.fromJson,
    (TabBarItemEntity).toString(): TabBarItemEntity.fromJson,
    (TeenModeStatusEntity).toString(): TeenModeStatusEntity.fromJson,
    (TodoTogetherDetailEntity).toString(): TodoTogetherDetailEntity.fromJson,
    (TodoTogetherDetailStage6).toString(): TodoTogetherDetailStage6.fromJson,
    (TodoTogetherDetailStage6OtherEndMsg)
        .toString(): TodoTogetherDetailStage6OtherEndMsg.fromJson,
    (TodoTogetherDetailStage8).toString(): TodoTogetherDetailStage8.fromJson,
    (TodoTogetherDetailStage3).toString(): TodoTogetherDetailStage3.fromJson,
    (TodoTogetherDetailStage2).toString(): TodoTogetherDetailStage2.fromJson,
    (TodoTogetherDetailStage5).toString(): TodoTogetherDetailStage5.fromJson,
    (TodoTogetherDetailStage5MyEndMsg)
        .toString(): TodoTogetherDetailStage5MyEndMsg.fromJson,
    (TodoTogetherDetailStage4).toString(): TodoTogetherDetailStage4.fromJson,
    (TodoTogetherDetailStage1).toString(): TodoTogetherDetailStage1.fromJson,
    (TodoTogetherListEntity).toString(): TodoTogetherListEntity.fromJson,
    (TopicItemEntity).toString(): TopicItemEntity.fromJson,
    (TRTCCustomMsgEntity).toString(): TRTCCustomMsgEntity.fromJson,
    (UploadFileEntity).toString(): UploadFileEntity.fromJson,
    (UsePropResultEntity).toString(): UsePropResultEntity.fromJson,
    (UserAvatarEntity).toString(): UserAvatarEntity.fromJson,
    (UserCardBoxEntity).toString(): UserCardBoxEntity.fromJson,
    (UserIdCardEntity).toString(): UserIdCardEntity.fromJson,
    (UserInfoEntity).toString(): UserInfoEntity.fromJson,
    (UserLabelEntity).toString(): UserLabelEntity.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}