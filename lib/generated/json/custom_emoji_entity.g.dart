import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/custom_emoji_entity.dart';

CustomEmojiEntity $CustomEmojiEntityFromJson(Map<String, dynamic> json) {
  final CustomEmojiEntity customEmojiEntity = CustomEmojiEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    customEmojiEntity.id = id;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    customEmojiEntity.userId = userId;
  }
  final String? emoji = jsonConvert.convert<String>(json['emoji']);
  if (emoji != null) {
    customEmojiEntity.emoji = emoji;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    customEmojiEntity.createdDate = createdDate;
  }
  return customEmojiEntity;
}

Map<String, dynamic> $CustomEmojiEntityToJson(CustomEmojiEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['emoji'] = entity.emoji;
  data['createdDate'] = entity.createdDate;
  return data;
}

extension CustomEmojiEntityExtension on CustomEmojiEntity {
  CustomEmojiEntity copyWith({
    String? id,
    String? userId,
    String? emoji,
    String? createdDate,
  }) {
    return CustomEmojiEntity()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..emoji = emoji ?? this.emoji
      ..createdDate = createdDate ?? this.createdDate;
  }
}