import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/custom_emoji_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/custom_emoji_entity.g.dart';

@JsonSerializable()
class CustomEmojiEntity {
	String? id;
	String? userId;
	String? emoji;
	String? createdDate;

	CustomEmojiEntity();

	factory CustomEmojiEntity.fromJson(Map<String, dynamic> json) => $CustomEmojiEntityFromJson(json);

	Map<String, dynamic> toJson() => $CustomEmojiEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}