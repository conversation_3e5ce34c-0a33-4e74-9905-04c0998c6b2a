import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/pages/chat/custom_emoji/custom_emoji_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CustomEmojiPage extends StatefulWidget {
  const CustomEmojiPage({super.key});

  @override
  State<CustomEmojiPage> createState() => _CustomEmojiPageState();
}

class _CustomEmojiPageState extends State<CustomEmojiPage> {
  late CustomEmojiController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(CustomEmojiController());
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CustomEmojiController>(
      init: _controller,
      global: false,
      builder: (controller) {
        return Scaffold(
          backgroundColor: const Color(0xFFF5F5F5),
          appBar: _buildAppBar(controller),
          body: _buildBody(controller),
          bottomNavigationBar: controller.isEditMode
              ? _buildBottomDeleteButton(controller)
              : null,
        );
      },
    );
  }

  /// 构建导航栏
  CustomAppBar _buildAppBar(CustomEmojiController controller) {
    return CustomAppBar(
      title: "添加的单个表情",
      backgroundColor: Colors.transparent,
      leftWidget: controller.isEditMode
          ? null // 编辑模式时隐藏关闭按钮
          : TextButton(
              onPressed: () => Get.back(),
              child: Text(
                "关闭",
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14.sp,
                ),
              ),
            ),
      rightWidgets: [
        TextButton(
          onPressed: () => controller.toggleEditMode(),
          child: Text(
            controller.isEditMode ? "取消" : "整理",
            style: TextStyle(
              color: Colors.black,
              fontSize: 14.sp,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody(CustomEmojiController controller) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 0,
        bottom: controller.isEditMode ? 100.h : 16.w, // 编辑模式下增加底部间距
      ),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.w,
          childAspectRatio: 1.0,
        ),
        itemCount: controller.emojis!.length,
        itemBuilder: (context, index) {
          final emoji = controller.emojis![index];

          if (emoji.id == "-1") {
            // 添加表情按钮
            return _buildAddEmojiButton(controller);
          } else {
            // 表情项
            return _buildEmojiItem(controller, emoji, index);
          }
        },
      ),
    );
  }

  /// 构建添加表情按钮
  Widget _buildAddEmojiButton(CustomEmojiController controller) {
    return GestureDetector(
      onTap: controller.isEditMode
          ? null
          : () => controller.selectAndUploadEmoji(),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: controller.isUploading
            ? Center(
                child: SizedBox(
                  width: 20.w,
                  height: 20.w,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Colors.grey.shade600),
                  ),
                ),
              )
            : Icon(
                Icons.add,
                size: 32.w,
                color: Colors.grey.shade600,
              ),
      ),
    );
  }

  /// 构建表情项
  Widget _buildEmojiItem(
      CustomEmojiController controller, dynamic emoji, int index) {
    final isSelected = controller.isEmojiSelected(emoji.id ?? '');

    return GestureDetector(
      onTap: () {
        if (controller.isEditMode) {
          // 编辑模式下切换选中状态
          controller.toggleEmojiSelection(emoji.id ?? '');
        }
      },
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(5.r)),
            child: ImageUtils.getImage(
              emoji.emoji ?? '',
              double.infinity,
              double.infinity,
              fit: BoxFit.contain,
            ),
          ),
          // 编辑模式下显示选择状态
          if (controller.isEditMode)
            Positioned(
              top: 4.w,
              right: 4.w,
              child: Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey.shade400,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        size: 12.w,
                        color: Colors.white,
                      )
                    : null,
              ),
            ),
        ],
      ),
    );
  }

  /// 构建底部删除按钮
  Widget _buildBottomDeleteButton(CustomEmojiController controller) {
    return Container(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 12.h,
        bottom: MediaQuery.of(context).padding.bottom + 12.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: SizedBox(
          width: double.infinity,
          height: 44.h,
          child: ElevatedButton(
            onPressed: controller.selectedEmojiIds.isNotEmpty
                ? () {
                    ToastUtils.showDialog(
                      content: "确定要删除选中的表情吗？",
                      onConfirm: () => controller.deleteSelectedEmojis(),
                    );
                  }
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: controller.selectedEmojiIds.isNotEmpty
                  ? Colors.red
                  : Colors.grey.shade300,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              elevation: 0,
            ),
            child: Text(
              "删除 (${controller.selectedEmojiIds.length})",
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
