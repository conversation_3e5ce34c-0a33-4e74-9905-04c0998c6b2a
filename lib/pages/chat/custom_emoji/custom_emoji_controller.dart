import 'package:dada/model/custom_emoji_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class CustomEmojiController extends GetxController {
  List<CustomEmojiEntity>? emojis = [];

  // 编辑状态
  bool isEditMode = false;

  // 选中的表情ID列表
  Set<String> selectedEmojiIds = <String>{};

  // 是否正在上传
  bool isUploading = false;

  @override
  void onInit() {
    super.onInit();
    _getData();
  }

  /// 获取表情列表数据
  void _getData() async {
    try {
      List<CustomEmojiEntity>? result = await ApiService().emojiList();
      emojis = result ?? [];
      // 在列表开头添加一个特殊的添加按钮项
      emojis!.insert(0, CustomEmojiEntity.fromJson({"id": "-1"}));
    } catch (e) {
      // 如果获取失败，至少保证有添加按钮
      emojis = [
        CustomEmojiEntity.fromJson({"id": "-1"})
      ];
      ToastUtils.showToast("获取表情列表失败");
    }
    update();
  }

  /// 刷新数据
  void refreshData() {
    _getData();
  }

  /// 切换编辑模式
  void toggleEditMode() {
    isEditMode = !isEditMode;
    if (!isEditMode) {
      // 退出编辑模式时清空选中状态
      selectedEmojiIds.clear();
    }
    update();
  }

  /// 切换表情选中状态
  void toggleEmojiSelection(String emojiId) {
    if (selectedEmojiIds.contains(emojiId)) {
      selectedEmojiIds.remove(emojiId);
    } else {
      selectedEmojiIds.add(emojiId);
    }
    update();
  }

  /// 检查表情是否被选中
  bool isEmojiSelected(String emojiId) {
    return selectedEmojiIds.contains(emojiId);
  }

  /// 选择并上传表情
  Future<void> selectAndUploadEmoji() async {
    if (isUploading) return;

    try {
      // 检查相册权限
      bool hasPermission =
          await ImagePickerUtil.checkPermission(1, "相册权限说明", "获取图片用于添加自定义表情");

      if (!hasPermission) return;

      // 选择图片
      List<AssetEntity>? assets =
          await ImagePickerUtil.selectAsset(maxAssets: 1, isImage: true);

      if (assets == null || assets.isEmpty) return;

      // 获取图片路径
      String? imagePath = await ImagePickerUtil.getEntityPath(assets.first);
      if (imagePath == null || imagePath.isEmpty) return;

      isUploading = true;
      update();

      // 上传图片到服务器
      String? uploadedUrl =
          await ApiService().uploadFile(imagePath, showLoading: true);
      if (uploadedUrl == null || uploadedUrl.isEmpty) {
        ToastUtils.showToast("图片上传失败");
        return;
      }

      // 保存表情到服务器
      bool success = await ApiService().addEmoji(uploadedUrl);
      if (success) {
        ToastUtils.showToast("表情添加成功");
        // 刷新页面数据
        refreshData();
      } else {
        ToastUtils.showToast("表情保存失败");
      }
    } catch (e) {
      ToastUtils.showToast("添加表情失败: $e");
    } finally {
      isUploading = false;
      update();
    }
  }

  /// 删除选中的表情
  Future<void> deleteSelectedEmojis() async {
    if (selectedEmojiIds.isEmpty) {
      ToastUtils.showToast("请选择要删除的表情");
      return;
    }

    try {
      List<String> failedIds = [];
      List<String> idsToDelete = selectedEmojiIds.toList();

      // 逐个删除选中的表情
      for (String emojiId in idsToDelete) {
        bool success = await ApiService().removeEmoji(emojiId);
        if (!success) {
          failedIds.add(emojiId);
        }
      }

      // 显示删除结果
      if (failedIds.isEmpty) {
        ToastUtils.showToast("删除成功");
      } else if (failedIds.length == idsToDelete.length) {
        ToastUtils.showToast("删除失败");
      } else {
        ToastUtils.showToast("部分表情删除失败");
      }

      // 清空选中状态并退出编辑模式
      selectedEmojiIds.clear();
      isEditMode = false;

      // 刷新页面数据
      refreshData();
    } catch (e) {
      ToastUtils.showToast("删除失败: $e");
      // 即使出错也要清空选中状态
      selectedEmojiIds.clear();
      isEditMode = false;
      update();
    }
  }
}
