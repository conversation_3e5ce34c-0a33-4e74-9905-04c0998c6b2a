import 'package:dada/common/values/colors.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/pages/chat/conversation/chat_conversation_list_controller.dart';
import 'package:dada/pages/chat/conversation/conversation_list_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

class ChatConversationListPage extends StatefulWidget {
  const ChatConversationListPage({super.key});

  @override
  State<ChatConversationListPage> createState() =>
      _ChatConversationListPageState();
}

class _ChatConversationListPageState extends State<ChatConversationListPage> {
  final ChatConversationListController controller =
      Get.put(ChatConversationListController());

  @override
  void initState() {
    super.initState();

    // WidgetsBinding.instance.addPostFrameCallback((timeStamp){
    //   controller.refreshData();
    // });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: controller,
      builder: (controller) {
        if (controller.conversationList == null ||
            controller.conversationList?.isEmpty == true) {
          return EmptyWidget(content: "暂无会话~");
        }
        return ListView.separated(
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) {
            V2TimConversation? conversation =
                controller.conversationList![index];
            if (conversation == null) {
              return Container();
            }
            return ConversationListItemWidget(conversation: conversation);
          },
          separatorBuilder: (controller, index) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              height: 1,
              color: AppColors.colorFFE5E5E5,
            );
          },
          itemCount: controller.conversationList!.length,
        );
      },
    );
  }
}
