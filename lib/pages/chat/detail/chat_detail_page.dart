import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/components/widgets/custom_add_face_tooltip_item.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_audio_msg_tooltip_item.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/components/widgets/user_medal_tag_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/apply/group_chat_apply_list_page.dart';
import 'package:dada/pages/chat/detail/card_box/user_card_box_bottom_sheet.dart';
import 'package:dada/pages/chat/detail/chat_custom_msg_item.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/pages/chat/detail/chat_side_button.dart';
import 'package:dada/pages/chat/setting/group/group_chat_settings_page.dart';
import 'package:dada/pages/chat/setting/private_chat_settings_page.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/dress_utils.dart';
import 'package:dada/utils/emoji_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKItMessageList/tim_uikit_chat_history_message_list_config.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitMessageItem/TIMUIKitMessageReaction/tim_uikit_message_reaction_wrapper.dart';
import 'package:tolyui/tolyui.dart';

class ChatDetailPage extends StatefulWidget {
  const ChatDetailPage({super.key});

  @override
  State<ChatDetailPage> createState() => _ChatDetailPageState();
}

class _ChatDetailPageState extends State<ChatDetailPage> {
  late ChatDetailController controller;
  V2TimConversation? conversation;

  @override
  void initState() {
    super.initState();

    conversation = Get.arguments;
    controller = ChatDetailController();
  }

  @override
  Widget build(BuildContext context) {
    if (conversation == null) {
      return const LoadingWidget();
    }
    return Stack(
      children: [
        TIMUIKitChat(
          isMonthCardUser: UserService().checkIsMonthCardUser(),
          onMonthCardFailed: () {
            ToastUtils.showBottomDialog(
                const MonthCardExpiredLimitChatDialog());
          },
          conversation: conversation!,
          controller: controller.timUIKitChatController,
          extraTipsActionItemBuilder: (message, closeTip, [key, context]) {
            return Container();
          },
          mainHistoryListConfig: TIMUIKitHistoryMessageListConfig(
            padding: EdgeInsets.only(bottom: 12.h),
          ),
          customAppBar: CustomAppBar(
            // backgroundColor: AppColors.colorFFF5F5F5,
            centerWidget: _buildCenterWidget(),
            rightWidgets: [_buildRightBarBtnWidget()],
          ),
          userAvatarBuilder: (BuildContext context, V2TimMessage message) {
            if (message.customElem != null &&
                message.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM) {
              String? jsonStr = message.customElem?.data;
              if (jsonStr != null) {
                if (StringUtils.isJsonString(jsonStr)) {
                  ChatImCustomMsgEntity customMsgEntity =
                      ChatImCustomMsgEntity.fromJson(jsonDecode(jsonStr));
                  String? type = customMsgEntity.type;
                  if (type == ChatImCustomMsgType.ChatGreetTipsMsg) {
                    return Container();
                  }
                }
              }
            }
            String? avatarFrameUrl = DressUtils()
                .getMessageUserDressUrl(message, DressUpType.avatar);
            return AvatarWidget(
              size: 50,
              url: message.faceUrl ?? "",
              dressUp: avatarFrameUrl,
              showPlaceholder: true,
              showDressUp: true,
            );
          },
          config: TIMUIKitChatConfig(
            isAllowLongPressAvatarToAt: conversation?.type != 1,
            isShowGroupReadingStatus: false,
            isUseMessageReaction: false,
            isShowReadingStatus: false,
            isAllowEmojiPanel: true,
            stickerPanelConfig: StickerPanelConfig(
              useQQStickerPackage: false,
              useTencentCloudChatStickerPackage: false,
              customStickerPackages:
                  EmojiUtils().customEmojiStickerPackageList ?? [],
            ),
          ),
          onTapAvatar: (userId, tapDetails) {
            Get.toNamed(GetRouter.userProfile, parameters: {"userId": userId});
          },
          morePanelConfig: MorePanelConfig(
            showFilePickAction: false,
            extraAction: _buildExtraMorePanelActions(),
          ),
          textFieldHintText: "请输入文字",
          toolTipsConfig: ToolTipsConfig(
            showForwardMessage: false,
            showMultipleChoiceMessage: false,
            showTranslation: false,
            additionalMessageToolTips: (message, closeToolTip) {
              if (message.elemType == MessageElemType.V2TIM_ELEM_TYPE_SOUND) {
                return [
                  CustomAudioMsgTooltipItem.build(
                      message: message, closeTooltip: closeToolTip),
                ];
              } else if (message.elemType ==
                  MessageElemType.V2TIM_ELEM_TYPE_FACE) {
                return [
                  CustomAddFaceTooltipItem.build(
                      message: message,
                      closeTooltip: closeToolTip,
                      onClick: () {
                        setState(() {});
                      })
                ];
              }
              return [];
            },
          ),
          customEmojiStickerList: EmojiUtils().customEmojiFaceDataList ?? [],
          onAddCustomEmoji: () {
            Get.toNamed(GetRouter.customEmoji, arguments: {
              "controller": controller.timUIKitChatController
            })?.then((v) {
              EmojiUtils().loadEmojiFiles().then((res) {
                setState(() {});
              });
            });
          },
          messageItemBuilder: MessageItemBuilder(
            customMessageItemBuilder:
                (V2TimMessage message, isShowJump, clearJump) {
              if (message.customElem != null &&
                  message.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM) {
                return ChatCustomMsgItem(
                  message: message,
                  chatDetailController: controller,
                );
              }
              return Container();
            },
            faceMessageItemBuilder: (message, isShowJump, clearJump) {
              if ((message.faceElem!.data ?? "").startsWith('http')) {
                return Container(
                  padding: const EdgeInsets.all(10),
                  constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width * 0.3),
                  child: CachedNetworkImage(imageUrl: message.faceElem!.data!),
                );
              }
              return null;
            },
          ),
          onBottomChange: (height) {
            WidgetsBinding.instance.addPostFrameCallback((time) {
              controller.updateBottomHeight(height);
            });
          },
          messageItemThemeData: MessageThemeData(
            messageTextColor: (message) {
              String? colorStr = DressUtils().getMessageUserDressUrl(
                  message, DressUpType.chatBox,
                  propertyType: 1);
              if (colorStr?.isNotEmpty == true) {
                colorStr = "FF$colorStr";
                Color color = Color(int.parse(colorStr, radix: 16));
                return color;
              }
              return Colors.black;
            },
            messageBubbleBuilder: (message, child) {
              ///获取气泡框Url
              String? chatBubbleDressUrl = DressUtils()
                  .getMessageUserDressUrl(message, DressUpType.chatBox);
              if (!(chatBubbleDressUrl?.isNotEmpty == true)) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  constraints: const BoxConstraints(
                    minWidth: 40,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: child,
                );
              }

              ///获取气泡框可拉伸区域
              String? chatBubbleSliceStr = DressUtils().getMessageUserDressUrl(
                  message, DressUpType.chatBox,
                  propertyType: 2);
              Rect? centerSlice =
                  StringUtils.stringToRect(chatBubbleSliceStr ?? "");
              if (centerSlice != null) {
                centerSlice = Rect.fromLTRB(
                    centerSlice.left / 3,
                    centerSlice.top / 3,
                    centerSlice.right / 3,
                    centerSlice.bottom / 3);
              }
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                constraints: const BoxConstraints(
                  minWidth: 40,
                ),
                decoration: BoxDecoration(
                  image: DecorationImage(
                    centerSlice:
                        centerSlice ?? const Rect.fromLTRB(18.5, 10, 20, 15),
                    image: CachedNetworkImageProvider(chatBubbleDressUrl!),
                    scale: 3,
                    fit: BoxFit.fill,
                  ),
                ),
                child: child,
              );
            },
          ),
        ),
        _showSideButton(),
        _showFriendRoleImageWidget(),
      ],
    );
  }

  Widget _buildCenterWidget() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _getAppBarTitle(),
              style: TextStyles.common(16.sp, Colors.black),
            ),
            GetBuilder(
              init: controller,
              global: false,
              filter: (controller) => controller.friendUserInfo?.isOnline == 1,
              builder: (controller) {
                if (controller.friendUserInfo == null) {
                  return Container();
                }
                return Visibility(
                  visible: controller.friendUserInfo?.isOnline == 1,
                  child: Padding(
                    padding: EdgeInsets.only(left: 5.w),
                    child: Container(
                      width: 6.w,
                      height: 6.w,
                      decoration: BoxDecoration(
                        color: AppColors.colorFF23AF28,
                        borderRadius: BorderRadius.circular(3.w),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        GetBuilder(
          init: controller,
          global: false,
          builder: (controller) {
            if (controller.friendUserInfo == null) {
              return Container();
            }
            UserInfoEntity userInfo =
                UserInfoEntity.fromJson(controller.friendUserInfo!.toJson());
            return UserMedalTagWidget(
              userInfo: userInfo,
              placement: Placement.bottom,
            );
          },
        ),
      ],
    );
  }

  String _getAppBarTitle() {
    String title = "";
    if (conversation?.type != null) {
      if (conversation!.type == 1) {
        title = conversation!.showName ?? "";
      } else {
        String? groupName = conversation!.showName;
        if (groupName?.isNotEmpty == true) {
          title = "${conversation!.showName}";
          if (controller.groupInfo != null) {
            title =
                "${getGroupChatTitle(conversation!.showName!)}（${controller.groupInfo!.memberCount ?? 0}）";
          }
        }
      }
    }
    return title;
  }

  Widget _buildRightBarBtnWidget() {
    return GetBuilder(
      init: controller,
      global: false,
      builder: (controller) {
        if (conversation?.type == 2) {
          return _buildGroupChatRightBarBtn();
        } else {
          return _buildPrivateMoreBtn();
        }
      },
    );
  }

  Widget _buildPrivateMoreBtn() {
    if (controller.friendUserInfo?.isFriend == 1) {
      return Padding(
        padding: EdgeInsets.only(right: 15.w),
        child: GestureDetector(
          onTap: () {
            if (controller.friendUserInfo != null) {
              Get.to(
                () => PrivateChatSettingsPage(
                    friendUserInfo: controller.friendUserInfo!),
              )?.then((result) {
                if (result == 1) {
                  controller.loadData();
                }
              });
            }
          },
          child: Icon(
            Icons.more_horiz,
            size: 20.w,
            color: AppColors.colorFF999999,
          ),
        ),
      );
    }
    return Container(
      width: 35.w,
    );
  }

  Widget _buildGroupChatRightBarBtn() {
    if (controller.groupInfo == null) {
      return Container();
    }
    return Padding(
      padding: EdgeInsets.only(right: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FutureBuilder(
            future: controller.checkIsGroupOwnerOrManager(),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                if (snapshot.data == true) {
                  return Obx(
                    () => Padding(
                      padding: EdgeInsets.only(right: 15.w),
                      child: BadgeWidget(
                        visible: controller.hasNewApplication.value,
                        smallSize: 6.w,
                        text: null,
                        child: GestureDetector(
                          onTap: () {
                            if (controller.conversation.groupID != null) {
                              Get.to(
                                () => GroupChatApplyListPage(
                                  groupID: controller.conversation.groupID!,
                                ),
                              );
                            }
                          },
                          child: ImageUtils.getImage(
                            Assets.imagesChatDetailRightBarBtnGroupInvite,
                            18.w,
                            18.w,
                          ),
                        ),
                      ),
                    ),
                  );
                }
              }
              return Container();
            },
          ),
          GestureDetector(
            onTap: () {
              if (controller.groupInfo != null) {
                Get.to(
                  () => GroupChatSettingsPage(groupInfo: controller.groupInfo!),
                );
              }
            },
            child: ImageUtils.getImage(
              Assets.imagesChatDetailRightBarBtnGroupSetting,
              12.w,
              13.w,
            ),
          ),
        ],
      ),
    );
  }

  List<MorePanelItem> _buildExtraMorePanelActions() {
    List<MorePanelItem> list = [];
    list.add(
      MorePanelItem(
        id: "audio_box",
        title: "语音盒子",
        onTap: (c) {
          Get.toNamed(GetRouter.audioBox, parameters: {
            "isSelect": "1"
          }, arguments: {
            "groupID": conversation?.groupID,
            "userID": conversation?.userID,
            "chatDetailController": controller,
          });
        },
        icon: Container(
          height: 64,
          width: 64,
          margin: const EdgeInsets.only(bottom: 4),
          alignment: Alignment.center,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(5))),
          child: ImageUtils.getImage(
              Assets.imagesChatDetailMorePanelAudioBox, 22.w, 22.w,
              fit: BoxFit.scaleDown),
        ),
      ),
    );

    list.add(
      MorePanelItem(
        id: "card_box",
        title: "卡盒",
        onTap: (c) {
          Get.bottomSheet(
            UserCardBoxBottomSheet(
              onSelectedCard: (cardInfo) {
                controller.sendCustomMsg(jsonEncode(cardInfo),
                    ChatImCustomMsgType.UserCardBoxCardMsg);
              },
            ),
          );
        },
        icon: Container(
          height: 50,
          width: 50,
          margin: const EdgeInsets.only(bottom: 4),
          alignment: Alignment.center,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(5))),
          child: ImageUtils.getImage(
              Assets.imagesChatDetailMorePanelCardBox, 20.w, 18.h),
        ),
      ),
    );
    return list;
  }

  Widget _showSideButton() {
    return GetBuilder(
        init: controller,
        global: false,
        builder: (controller) {
          return Visibility(
            visible: conversation?.type == 1 &&
                controller.friendUserInfo?.isDaziRelation() == true,
            child: Obx(() {
              return AnimatedPositioned(
                  left: 10.w,
                  bottom: max(200.w, controller.bottomHeight.value + 160.w),
                  curve: Curves.fastOutSlowIn,
                  duration: const Duration(milliseconds: 200),
                  child: ChatSideButton(
                    daUserId: controller.friendUserInfo?.userFriendId ?? "",
                    friendLevel: controller.friendUserInfo?.friendLevel ?? "",
                  ));
            }),
          );
        });
  }

  Widget _showFriendRoleImageWidget() {
    double bottom = ScreenUtil().bottomBarHeight;
    if (Platform.isAndroid) {
      bottom += 40.0;
    } else if (Platform.isIOS) {
      bottom += 10.h;
    }
    return GetBuilder(
        init: controller,
        global: false,
        builder: (controller) {
          String roleImage = "";
          if (controller.friendUserInfo?.currentDressNo?.isNotEmpty == true) {
            roleImage = DressUtils().getSvgaAssetNameWithDressNo(
                dressNo: controller.friendUserInfo!.currentDressNo!,
                status: "SIT_PLAY_PHONE");
          }
          if (roleImage.isEmpty) {
            return Container();
          }
          return Visibility(
            visible: conversation?.type == 1 &&
                controller.friendUserInfo?.isDaziRelation() == true,
            child: Obx(() {
              return AnimatedPositioned(
                left: 0.w,
                bottom: max(bottom, controller.bottomHeight.value + bottom),
                curve: Curves.fastOutSlowIn,
                duration: const Duration(milliseconds: 200),
                child: Container(
                  width: 137.w,
                  height: 137.w,
                  alignment: Alignment.bottomLeft,
                  child: Stack(
                    alignment: Alignment.bottomLeft,
                    children: [
                      Container(
                        padding: EdgeInsets.only(right: 15.w, top: 10.h),
                        child: ImageUtils.getImage(
                            Assets.imagesChatDetailDaziRoleImgeBg,
                            137.w,
                            137.w),
                      ),
                      Transform.scale(
                        scaleX: -1,
                        child: SizedBox(
                          width: 100.w,
                          height: 110.h,
                          child: SVGASimpleImage(
                            assetsName: "assets/svga/dress/$roleImage",
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
          );
        });
  }

  String getGroupChatTitle(String groupName) {
    if (groupName.length > 8) {
      return "${groupName.substring(0, 8)}...";
    }
    return groupName;
  }

  @override
  void dispose() {
    controller.stopAudioPlayer();

    super.dispose();
  }
}
