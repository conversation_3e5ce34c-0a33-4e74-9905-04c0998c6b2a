import 'dart:io';

import 'package:dada/configs/app_config.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';

class AdPluginManager {
  static AdPluginManager? _instance;

  static AdPluginManager get instance {
    _instance ??= AdPluginManager._internal();
    return _instance!;
  }

  AdPluginManager._internal() {
    // _initAdPlugin();
    _addEventListener();
  }

  final List<AdPluginListener> _listeners = [];

  void _initAdPlugin() async {
    bool result = await FlutterGromoreAds.initAd(
        Platform.isIOS
            ? AppConfig.gromoreAdsAppId_iOS
            : AppConfig.gromoreAdsAppId_Android,
        useMediation: true,
        themeStatus: 1);
    if (result) {
      debugPrint("Ad Plugin 初始化成功");
      _addEventListener();
    } else {
      debugPrint("Ad Plugin 初始化失败");
    }
  }

  void _addEventListener() {
    FlutterGromoreAds.onEventListener((event) {
      debugPrint(
          "Ad Plugin onEventListener, adId: ${event.adId}, action: ${event.action}");
      switch (event.action) {
        case AdEventAction.onAdError:
          debugPrint("Ad Plugin onAdError");
          AdErrorEvent errorEvent = event as AdErrorEvent;
          ToastUtils.hideLoading();
          ToastUtils.showToast("${errorEvent.errMsg}");
          _notifyListener(event);
          break;
        case AdEventAction.onAdLoaded:
          ToastUtils.hideLoading();
          debugPrint("Ad Plugin onAdLoaded");
          _notifyListener(event);
          break;
        case AdEventAction.onAdPresent:
          debugPrint("Ad Plugin onAdPresent");
          _notifyListener(event);
          break;
        case AdEventAction.onAdExposure:
          debugPrint("Ad Plugin onAdExposure");
          _notifyListener(event);
          break;
        case AdEventAction.onAdClosed:
          debugPrint("Ad Plugin onAdClosed");
          _notifyListener(event);
          break;
        case AdEventAction.onAdClicked:
          debugPrint("Ad Plugin onAdClicked");
          break;
        case AdEventAction.onAdSkip:
          _notifyListener(event);
          break;
        case AdEventAction.onAdComplete:
          debugPrint("Ad Plugin onAdComplete");
          _notifyListener(event);
          break;
        case AdEventAction.onAdReward:
          debugPrint("Ad Plugin onAdReward");
          _notifyListener(event);
          break;
        case AdEventAction.onAdEcpm:
          break;
      }
    });
  }

  ///预加载激励视频广告
  void preLoadRewardVideoAd(String adId) {
    FlutterGromoreAds.preload(rewardPosids: [adId]);
  }

  ///显示激励视频广告
  void showRewardVideoAd(String adId) async {
    ToastUtils.showLoading();
    bool success = await FlutterGromoreAds.showRewardVideoAd(
      adId,
      customData: 'customData',
      userId: "userId",
    );
    ToastUtils.hideLoading();
    debugPrint("showRewardVideoAd: $success");
  }

  void _notifyListener(AdEvent event) {
    for (final listener in _listeners) {
      if (event.action == AdEventAction.onAdLoaded) {
        listener.onAdLoaded(event.adId);
      } else if (event.action == AdEventAction.onAdClosed) {
        listener.onAdClosed(event.adId);
      } else if (event.action == AdEventAction.onAdSkip) {
        listener.onAdSkip(event.adId);
      } else if (event.action == AdEventAction.onAdPresent) {
        listener.onAdPresent(event.adId);
      } else if (event.action == AdEventAction.onAdExposure) {
        listener.onAdExposure(event.adId);
      } else if (event.action == AdEventAction.onAdComplete) {
        listener.onAdComplete(event.adId);
      }
      if (event is AdErrorEvent) {
        listener.onAdError(event);
      }
      if (event is AdRewardEvent) {
        listener.onAdReward(event);
      }
    }
  }

  void addListener(AdPluginListener listener) {
    _listeners.add(listener);
  }

  void removeListener(AdPluginListener listener) {
    _listeners.remove(listener);
  }
}

class AdPluginListener {
  Function(String adId) onAdLoaded = (String adId) {};
  Function(String adId) onAdPresent = (String adId) {};
  Function(String adId) onAdExposure = (String adId) {};
  Function(String adId) onAdClicked = (String adId) {};
  Function(String adId) onAdClosed = (String adId) {};
  Function(String adId) onAdSkip = (String adId) {};
  Function(String adId) onAdComplete = (String adId) {};
  Function(AdErrorEvent) onAdError = (AdErrorEvent error) {};
  Function(AdRewardEvent reward) onAdReward = (AdRewardEvent reward) {};

  AdPluginListener({
    Function(String adId)? onAdLoaded,
    Function(String adId)? onAdPresent,
    Function(String adId)? onAdExposure,
    Function(String adId)? onAdClicked,
    Function(String adId)? onAdClosed,
    Function(String adId)? onAdSkip,
    Function(String adId)? onAdComplete,
    Function(AdErrorEvent)? onAdError,
    Function(AdRewardEvent reward)? onAdReward,
  }) {
    if (onAdReward != null) {
      this.onAdReward = onAdReward;
    }
    if (onAdError != null) {
      this.onAdError = onAdError;
    }
    if (onAdLoaded != null) {
      this.onAdLoaded = onAdLoaded;
    }
    if (onAdPresent != null) {
      this.onAdPresent = onAdPresent;
    }
    if (onAdExposure != null) {
      this.onAdExposure = onAdExposure;
    }
    if (onAdClicked != null) {
      this.onAdClicked = onAdClicked;
    }
    if (onAdClosed != null) {
      this.onAdClosed = onAdClosed;
    }
    if (onAdComplete != null) {
      this.onAdComplete = onAdComplete;
    }
  }
}
